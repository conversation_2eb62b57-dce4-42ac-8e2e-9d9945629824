using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Test
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class InterviewTestModel : PageModel
    {
        private readonly IInterviewService _interviewService;
        private readonly ICandidateService _candidateService;
        private readonly ILLMModelService _llmModelService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<InterviewTestModel> _logger;

        public InterviewTestModel(
            IInterviewService interviewService,
            ICandidateService candidateService,
            ILLMModelService llmModelService,
            IRoleManagementService roleManagementService,
            ILogger<InterviewTestModel> logger)
        {
            _interviewService = interviewService;
            _candidateService = candidateService;
            _llmModelService = llmModelService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        public string TestResults { get; set; } = "";

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                var results = new List<string>();

                // Test 1: Check if InterviewService is registered
                results.Add("✅ InterviewService is properly registered and injected");

                // Test 2: Check if we can get user roles
                var userRoles = await _roleManagementService.GetUserRolesAsync(userId);
                results.Add($"✅ User roles retrieved: {string.Join(", ", userRoles)}");

                // Test 3: Check if we can get Corp Admin ID
                var corpAdminId = await _roleManagementService.GetCorpAdminIdAsync(userId);
                results.Add($"✅ Corp Admin ID: {corpAdminId ?? "null (Admin user)"}");

                // Test 4: Check if we can get active LLM models
                var llmModels = await _llmModelService.GetActiveModelsAsync();
                results.Add($"✅ Active LLM models count: {llmModels.Count()}");

                // Test 5: Check if we can get candidates (if not admin)
                if (!userRoles.Contains(UserRoles.Admin))
                {
                    var candidates = await _candidateService.GetCandidatesAsync(userId);
                    results.Add($"✅ Candidates for user: {candidates.Count()}");
                }
                else
                {
                    results.Add("ℹ️ Skipping candidate test for Admin user");
                }

                // Test 6: Check if we can get consultants in tenant
                if (!string.IsNullOrEmpty(corpAdminId))
                {
                    var consultants = await _roleManagementService.GetUsersInTenantByRoleAsync(corpAdminId, UserRoles.Consultant);
                    results.Add($"✅ Consultants in tenant: {consultants.Count()}");
                }
                else
                {
                    results.Add("ℹ️ Skipping consultant test for Admin user");
                }

                // Test 7: Check if we can get interviews by creator
                var interviews = await _interviewService.GetInterviewsByCreatorAsync(userId);
                results.Add($"✅ Interviews created by user: {interviews.Count()}");

                // Test 8: Check if we can get interview count
                var interviewCount = await _interviewService.GetInterviewCountByCreatorAsync(userId);
                results.Add($"✅ Interview count for user: {interviewCount}");

                TestResults = string.Join("\n", results);

                _logger.LogInformation("Interview test completed successfully for user {UserId}", userId);

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during interview test for user {UserId}", User.Identity?.Name);
                TestResults = $"❌ Error during test: {ex.Message}\n\nStack trace:\n{ex.StackTrace}";
                return Page();
            }
        }

        public async Task<IActionResult> OnPostCreateTestInterviewAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return new JsonResult(new { success = false, message = "User not authenticated" });
                }

                // Get first available candidate, consultant, and LLM model
                var candidates = await _candidateService.GetCandidatesAsync(userId);
                var firstCandidate = candidates.FirstOrDefault();

                if (firstCandidate == null)
                {
                    return new JsonResult(new { success = false, message = "No candidates available for testing" });
                }

                var corpAdminId = await _roleManagementService.GetCorpAdminIdAsync(userId);
                if (string.IsNullOrEmpty(corpAdminId))
                {
                    return new JsonResult(new { success = false, message = "Cannot determine Corp Admin for user" });
                }

                var consultants = await _roleManagementService.GetUsersInTenantByRoleAsync(corpAdminId, UserRoles.Consultant);
                var firstConsultant = consultants.FirstOrDefault();

                if (firstConsultant == null)
                {
                    return new JsonResult(new { success = false, message = "No consultants available for testing" });
                }

                var llmModels = await _llmModelService.GetActiveModelsAsync();
                var firstLlmModel = llmModels.FirstOrDefault();

                if (firstLlmModel == null)
                {
                    return new JsonResult(new { success = false, message = "No active LLM models available for testing" });
                }

                // Create test interview
                var testInterview = new Interview
                {
                    JobDescription = "Test job description for interview functionality testing. This is a sample job that requires various skills and experience.",
                    InterviewDateTimeUtc = DateTime.UtcNow.AddDays(1),
                    CandidateId = firstCandidate.Id,
                    ConsultantId = firstConsultant.Id,
                    LLMModelId = firstLlmModel.Id,
                    Notes = "This is a test interview created by the interview test functionality."
                };

                var createdInterview = await _interviewService.CreateInterviewAsync(testInterview, userId);

                _logger.LogInformation("Test interview {InterviewId} created successfully by user {UserId}", 
                    createdInterview.Id, userId);

                return new JsonResult(new { 
                    success = true, 
                    message = "Test interview created successfully", 
                    interviewId = createdInterview.Id 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test interview for user {UserId}", User.Identity?.Name);
                return new JsonResult(new { success = false, message = $"Error creating test interview: {ex.Message}" });
            }
        }
    }
}
