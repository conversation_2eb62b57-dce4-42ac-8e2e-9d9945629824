using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Data;

namespace RecruiterBot.Infrastructure.Services
{
    public class InterviewService : IInterviewService
    {
        private readonly IMongoCollection<Interview> _interviews;
        private readonly ILogger<InterviewService> _logger;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ICandidateService _candidateService;
        private readonly ILLMModelService _llmModelService;
        private readonly IEmailService _emailService;
        private const string CollectionName = "Interviews";

        public InterviewService(
            IOptions<MongoDbSettings> settings,
            ILogger<InterviewService> logger,
            IRoleManagementService roleManagementService,
            ICandidateService candidateService,
            ILLMModelService llmModelService,
            IEmailService emailService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _roleManagementService = roleManagementService ?? throw new ArgumentNullException(nameof(roleManagementService));
            _candidateService = candidateService ?? throw new ArgumentNullException(nameof(candidateService));
            _llmModelService = llmModelService ?? throw new ArgumentNullException(nameof(llmModelService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));

            if (settings?.Value == null)
                throw new ArgumentNullException(nameof(settings));

            var client = new MongoClient(settings.Value.ConnectionString);
            var database = client.GetDatabase(settings.Value.DatabaseName);
            _interviews = database.GetCollection<Interview>(CollectionName);

            // Create indexes
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            try
            {
                // Create index on tenant for multi-tenancy
                var tenantIndexKeys = Builders<Interview>.IndexKeys.Ascending(x => x.Tenant);
                var tenantIndexModel = new CreateIndexModel<Interview>(tenantIndexKeys);

                // Create index on consultant for efficient consultant queries
                var consultantIndexKeys = Builders<Interview>.IndexKeys.Ascending(x => x.ConsultantId);
                var consultantIndexModel = new CreateIndexModel<Interview>(consultantIndexKeys);

                // Create index on created by for creator queries
                var createdByIndexKeys = Builders<Interview>.IndexKeys.Ascending(x => x.CreatedBy);
                var createdByIndexModel = new CreateIndexModel<Interview>(createdByIndexKeys);

                // Create index on interview date for time-based queries
                var dateIndexKeys = Builders<Interview>.IndexKeys.Ascending(x => x.InterviewDateTimeUtc);
                var dateIndexModel = new CreateIndexModel<Interview>(dateIndexKeys);

                // Create compound index on tenant and status
                var tenantStatusIndexKeys = Builders<Interview>.IndexKeys
                    .Ascending(x => x.Tenant)
                    .Ascending(x => x.Status);
                var tenantStatusIndexModel = new CreateIndexModel<Interview>(tenantStatusIndexKeys);

                // Create compound index on consultant and date for upcoming interviews
                var consultantDateIndexKeys = Builders<Interview>.IndexKeys
                    .Ascending(x => x.ConsultantId)
                    .Ascending(x => x.InterviewDateTimeUtc);
                var consultantDateIndexModel = new CreateIndexModel<Interview>(consultantDateIndexKeys);

                _interviews.Indexes.CreateMany(new[]
                {
                    tenantIndexModel,
                    consultantIndexModel,
                    createdByIndexModel,
                    dateIndexModel,
                    tenantStatusIndexModel,
                    consultantDateIndexModel
                });

                _logger.LogInformation("Created indexes for {CollectionName} collection", CollectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating indexes for {CollectionName} collection", CollectionName);
            }
        }

        public async Task<Interview> CreateInterviewAsync(Interview interview, string userId)
        {
            if (interview == null)
                throw new ArgumentNullException(nameof(interview));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get user's corp admin ID for tenant
                var corpAdminId = await _roleManagementService.GetCorpAdminIdAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                {
                    throw new InvalidOperationException("Unable to determine Corp Admin for user");
                }

                // Validate that candidate exists and belongs to the same tenant
                var candidate = await _candidateService.GetCandidateAsync(interview.CandidateId, userId);
                if (candidate == null)
                {
                    throw new InvalidOperationException("Candidate not found or access denied");
                }

                // Validate that LLM model exists and is active
                var llmModel = await _llmModelService.GetModelByIdAsync(interview.LLMModelId);
                if (llmModel == null || !llmModel.IsActive)
                {
                    throw new InvalidOperationException("LLM model not found or inactive");
                }

                // Validate that consultant exists and belongs to the same tenant
                var consultant = await _roleManagementService.GetUserByIdAsync(interview.ConsultantId);
                if (consultant == null || consultant.Tenant != corpAdminId)
                {
                    throw new InvalidOperationException("Consultant not found or access denied");
                }

                // Validate that consultant has the CONSULTANT role
                var consultantRoles = await _roleManagementService.GetUserRolesAsync(interview.ConsultantId);
                if (!consultantRoles.Contains(UserRoles.Consultant))
                {
                    throw new InvalidOperationException("Selected user is not a consultant");
                }

                interview.Tenant = corpAdminId;
                interview.CreatedBy = userId;
                interview.CreatedDateUtc = DateTime.UtcNow;
                interview.UpdatedDateUtc = DateTime.UtcNow;
                interview.Status = InterviewStatus.Scheduled;
                interview.IsActive = true;

                await _interviews.InsertOneAsync(interview);

                _logger.LogInformation("Created interview {InterviewId} for user {UserId} with tenant {TenantId}",
                    interview.Id, userId, corpAdminId);

                // Load related data for the notification
                await LoadInterviewRelatedDataAsync(interview);

                // Send notification to the assigned consultant
                await SendInterviewAssignmentNotificationAsync(interview, userId);

                return interview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating interview for user {UserId}", userId);
                throw;
            }
        }

        public async Task<Interview?> GetInterviewByIdAsync(string interviewId, string userId)
        {
            if (string.IsNullOrWhiteSpace(interviewId))
                return null;

            if (string.IsNullOrWhiteSpace(userId))
                return null;

            try
            {
                var interview = await _interviews
                    .Find(i => i.Id == interviewId)
                    .FirstOrDefaultAsync();

                if (interview == null)
                    return null;

                // Check access permissions
                if (!await CanUserAccessInterviewAsync(interview, userId))
                {
                    _logger.LogWarning("User {UserId} attempted to access interview {InterviewId} without permission", 
                        userId, interviewId);
                    return null;
                }

                // Load related data
                await LoadInterviewRelatedDataAsync(interview);

                return interview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interview {InterviewId} for user {UserId}", interviewId, userId);
                return null;
            }
        }

        private async Task<bool> CanUserAccessInterviewAsync(Interview interview, string userId)
        {
            try
            {
                var userRoles = await _roleManagementService.GetUserRolesAsync(userId);
                var corpAdminId = await _roleManagementService.GetCorpAdminIdAsync(userId);

                // Admin can access all interviews
                if (userRoles.Contains(UserRoles.Admin))
                    return true;

                // Corp Admin can access interviews in their tenant
                if (userRoles.Contains(UserRoles.CorpAdmin) && interview.Tenant == userId)
                    return true;

                // Standard User can access interviews they created
                if (userRoles.Contains(UserRoles.User) && interview.CreatedBy == userId)
                    return true;

                // Consultant can access interviews assigned to them
                if (userRoles.Contains(UserRoles.Consultant) && interview.ConsultantId == userId)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking access for user {UserId} to interview {InterviewId}", 
                    userId, interview.Id);
                return false;
            }
        }

        private async Task LoadInterviewRelatedDataAsync(Interview interview)
        {
            try
            {
                // Load candidate
                if (!string.IsNullOrWhiteSpace(interview.CandidateId))
                {
                    interview.Candidate = await _candidateService.GetCandidateAsync(interview.CandidateId, interview.CreatedBy);
                }

                // Load consultant
                if (!string.IsNullOrWhiteSpace(interview.ConsultantId))
                {
                    interview.Consultant = await _roleManagementService.GetUserByIdAsync(interview.ConsultantId);
                }

                // Load LLM model
                if (!string.IsNullOrWhiteSpace(interview.LLMModelId))
                {
                    interview.LLMModel = await _llmModelService.GetModelByIdAsync(interview.LLMModelId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading related data for interview {InterviewId}", interview.Id);
                // Continue without related data
            }
        }

        public async Task<Interview> UpdateInterviewAsync(Interview interview, string userId)
        {
            if (interview == null)
                throw new ArgumentNullException(nameof(interview));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                var existingInterview = await GetInterviewByIdAsync(interview.Id, userId);
                if (existingInterview == null)
                {
                    throw new InvalidOperationException("Interview not found or access denied");
                }

                // Preserve creation info and update audit fields
                interview.CreatedBy = existingInterview.CreatedBy;
                interview.Tenant = existingInterview.Tenant;
                interview.CreatedDateUtc = existingInterview.CreatedDateUtc;
                interview.UpdatedDateUtc = DateTime.UtcNow;

                // Validate candidate, consultant, and LLM model if they changed
                if (interview.CandidateId != existingInterview.CandidateId)
                {
                    var candidate = await _candidateService.GetCandidateAsync(interview.CandidateId, userId);
                    if (candidate == null)
                    {
                        throw new InvalidOperationException("Candidate not found or access denied");
                    }
                }

                if (interview.ConsultantId != existingInterview.ConsultantId)
                {
                    var consultant = await _roleManagementService.GetUserByIdAsync(interview.ConsultantId);
                    if (consultant == null || consultant.Tenant != interview.Tenant)
                    {
                        throw new InvalidOperationException("Consultant not found or access denied");
                    }

                    var consultantRoles = await _roleManagementService.GetUserRolesAsync(interview.ConsultantId);
                    if (!consultantRoles.Contains(UserRoles.Consultant))
                    {
                        throw new InvalidOperationException("Selected user is not a consultant");
                    }
                }

                if (interview.LLMModelId != existingInterview.LLMModelId)
                {
                    var llmModel = await _llmModelService.GetModelByIdAsync(interview.LLMModelId);
                    if (llmModel == null || !llmModel.IsActive)
                    {
                        throw new InvalidOperationException("LLM model not found or inactive");
                    }
                }

                await _interviews.ReplaceOneAsync(i => i.Id == interview.Id, interview);

                _logger.LogInformation("Updated interview {InterviewId} by user {UserId}", interview.Id, userId);

                return interview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview {InterviewId} by user {UserId}", interview?.Id, userId);
                throw;
            }
        }

        public async Task<bool> DeleteInterviewAsync(string interviewId, string userId)
        {
            if (string.IsNullOrWhiteSpace(interviewId))
                return false;

            if (string.IsNullOrWhiteSpace(userId))
                return false;

            try
            {
                var interview = await GetInterviewByIdAsync(interviewId, userId);
                if (interview == null)
                {
                    return false;
                }

                // Soft delete by setting IsActive to false
                var update = Builders<Interview>.Update
                    .Set(i => i.IsActive, false)
                    .Set(i => i.UpdatedDateUtc, DateTime.UtcNow);

                var result = await _interviews.UpdateOneAsync(i => i.Id == interviewId, update);

                if (result.ModifiedCount > 0)
                {
                    _logger.LogInformation("Deleted interview {InterviewId} by user {UserId}", interviewId, userId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting interview {InterviewId} by user {UserId}", interviewId, userId);
                return false;
            }
        }

        public async Task<IEnumerable<Interview>> GetInterviewsByCreatorAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return Enumerable.Empty<Interview>();

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.CreatedBy, userId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var sort = Builders<Interview>.Sort.Descending(i => i.InterviewDateTimeUtc);

                var interviews = await _interviews
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                // Load related data for each interview
                foreach (var interview in interviews)
                {
                    await LoadInterviewRelatedDataAsync(interview);
                }

                _logger.LogDebug("Retrieved {Count} interviews for creator {UserId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    interviews.Count, userId, skip, limit, includeInactive);

                return interviews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interviews for creator {UserId}", userId);
                return Enumerable.Empty<Interview>();
            }
        }

        public async Task<IEnumerable<Interview>> GetInterviewsByConsultantAsync(string consultantId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(consultantId))
                return Enumerable.Empty<Interview>();

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.ConsultantId, consultantId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var sort = Builders<Interview>.Sort.Descending(i => i.InterviewDateTimeUtc);

                var interviews = await _interviews
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                // Load related data for each interview
                foreach (var interview in interviews)
                {
                    await LoadInterviewRelatedDataAsync(interview);
                }

                _logger.LogDebug("Retrieved {Count} interviews for consultant {ConsultantId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    interviews.Count, consultantId, skip, limit, includeInactive);

                return interviews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interviews for consultant {ConsultantId}", consultantId);
                return Enumerable.Empty<Interview>();
            }
        }

        public async Task<IEnumerable<Interview>> GetInterviewsByTenantAsync(string tenantId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
                return Enumerable.Empty<Interview>();

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.Tenant, tenantId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var sort = Builders<Interview>.Sort.Descending(i => i.InterviewDateTimeUtc);

                var interviews = await _interviews
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                // Load related data for each interview
                foreach (var interview in interviews)
                {
                    await LoadInterviewRelatedDataAsync(interview);
                }

                _logger.LogDebug("Retrieved {Count} interviews for tenant {TenantId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    interviews.Count, tenantId, skip, limit, includeInactive);

                return interviews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interviews for tenant {TenantId}", tenantId);
                return Enumerable.Empty<Interview>();
            }
        }

        public async Task<long> GetInterviewCountByCreatorAsync(string userId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return 0;

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.CreatedBy, userId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var count = await _interviews.CountDocumentsAsync(filter);

                _logger.LogDebug("Interview count for creator {UserId}: {Count} (includeInactive: {IncludeInactive})",
                    userId, count, includeInactive);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interview count for creator {UserId}", userId);
                return 0;
            }
        }

        public async Task<long> GetInterviewCountByConsultantAsync(string consultantId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(consultantId))
                return 0;

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.ConsultantId, consultantId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var count = await _interviews.CountDocumentsAsync(filter);

                _logger.LogDebug("Interview count for consultant {ConsultantId}: {Count} (includeInactive: {IncludeInactive})",
                    consultantId, count, includeInactive);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interview count for consultant {ConsultantId}", consultantId);
                return 0;
            }
        }

        public async Task<long> GetInterviewCountByTenantAsync(string tenantId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
                return 0;

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.Eq(i => i.Tenant, tenantId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(i => i.IsActive, true));
                }

                var count = await _interviews.CountDocumentsAsync(filter);

                _logger.LogDebug("Interview count for tenant {TenantId}: {Count} (includeInactive: {IncludeInactive})",
                    tenantId, count, includeInactive);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interview count for tenant {TenantId}", tenantId);
                return 0;
            }
        }

        public async Task<bool> UpdateInterviewStatusAsync(string interviewId, InterviewStatus status, string userId, string notes = null)
        {
            if (string.IsNullOrWhiteSpace(interviewId))
                return false;

            if (string.IsNullOrWhiteSpace(userId))
                return false;

            try
            {
                var interview = await GetInterviewByIdAsync(interviewId, userId);
                if (interview == null)
                {
                    return false;
                }

                var updateBuilder = Builders<Interview>.Update
                    .Set(i => i.Status, status)
                    .Set(i => i.UpdatedDateUtc, DateTime.UtcNow);

                if (!string.IsNullOrWhiteSpace(notes))
                {
                    updateBuilder = updateBuilder.Set(i => i.Notes, notes);
                }

                // Set completion or cancellation dates based on status
                if (status == InterviewStatus.Completed)
                {
                    updateBuilder = updateBuilder.Set(i => i.CompletedDateUtc, DateTime.UtcNow);
                }
                else if (status == InterviewStatus.Cancelled)
                {
                    updateBuilder = updateBuilder.Set(i => i.CancelledDateUtc, DateTime.UtcNow);
                    if (!string.IsNullOrWhiteSpace(notes))
                    {
                        updateBuilder = updateBuilder.Set(i => i.CancellationReason, notes);
                    }
                }

                var result = await _interviews.UpdateOneAsync(i => i.Id == interviewId, updateBuilder);

                if (result.ModifiedCount > 0)
                {
                    _logger.LogInformation("Updated interview {InterviewId} status to {Status} by user {UserId}",
                        interviewId, status, userId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview {InterviewId} status by user {UserId}", interviewId, userId);
                return false;
            }
        }

        public async Task<IEnumerable<Interview>> GetUpcomingInterviewsForConsultantAsync(string consultantId, int daysAhead = 7)
        {
            if (string.IsNullOrWhiteSpace(consultantId))
                return Enumerable.Empty<Interview>();

            try
            {
                var now = DateTime.UtcNow;
                var futureDate = now.AddDays(daysAhead);

                var filterBuilder = Builders<Interview>.Filter;
                var filter = filterBuilder.And(
                    filterBuilder.Eq(i => i.ConsultantId, consultantId),
                    filterBuilder.Eq(i => i.IsActive, true),
                    filterBuilder.Eq(i => i.Status, InterviewStatus.Scheduled),
                    filterBuilder.Gte(i => i.InterviewDateTimeUtc, now),
                    filterBuilder.Lte(i => i.InterviewDateTimeUtc, futureDate)
                );

                var sort = Builders<Interview>.Sort.Ascending(i => i.InterviewDateTimeUtc);

                var interviews = await _interviews
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();

                // Load related data for each interview
                foreach (var interview in interviews)
                {
                    await LoadInterviewRelatedDataAsync(interview);
                }

                _logger.LogDebug("Retrieved {Count} upcoming interviews for consultant {ConsultantId} (next {DaysAhead} days)",
                    interviews.Count, consultantId, daysAhead);

                return interviews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming interviews for consultant {ConsultantId}", consultantId);
                return Enumerable.Empty<Interview>();
            }
        }

        public async Task<IEnumerable<Interview>> SearchInterviewsAsync(string searchTerm, string userId, string userRole, int skip = 0, int limit = 50)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return Enumerable.Empty<Interview>();

            try
            {
                var filterBuilder = Builders<Interview>.Filter;
                var baseFilter = filterBuilder.Eq(i => i.IsActive, true);

                // Apply role-based filtering
                if (userRole == UserRoles.User)
                {
                    baseFilter = filterBuilder.And(baseFilter, filterBuilder.Eq(i => i.CreatedBy, userId));
                }
                else if (userRole == UserRoles.Consultant)
                {
                    baseFilter = filterBuilder.And(baseFilter, filterBuilder.Eq(i => i.ConsultantId, userId));
                }
                else if (userRole == UserRoles.CorpAdmin)
                {
                    baseFilter = filterBuilder.And(baseFilter, filterBuilder.Eq(i => i.Tenant, userId));
                }
                // Admin can see all interviews (no additional filter)

                FilterDefinition<Interview> searchFilter = baseFilter;

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    var textFilter = filterBuilder.Or(
                        filterBuilder.Regex(i => i.JobDescription, new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                        filterBuilder.Regex(i => i.Notes, new MongoDB.Bson.BsonRegularExpression(searchTerm, "i"))
                    );

                    searchFilter = filterBuilder.And(baseFilter, textFilter);
                }

                var sort = Builders<Interview>.Sort.Descending(i => i.InterviewDateTimeUtc);

                var interviews = await _interviews
                    .Find(searchFilter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                // Load related data for each interview
                foreach (var interview in interviews)
                {
                    await LoadInterviewRelatedDataAsync(interview);
                }

                _logger.LogDebug("Search returned {Count} interviews for user {UserId} with term '{SearchTerm}' (skip: {Skip}, limit: {Limit})",
                    interviews.Count, userId, searchTerm, skip, limit);

                return interviews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching interviews for user {UserId} with term '{SearchTerm}'", userId, searchTerm);
                return Enumerable.Empty<Interview>();
            }
        }

        private async Task SendInterviewAssignmentNotificationAsync(Interview interview, string createdByUserId)
        {
            try
            {
                if (interview.Consultant == null || string.IsNullOrWhiteSpace(interview.Consultant.Email))
                {
                    _logger.LogWarning("Cannot send interview assignment notification: consultant email not available for interview {InterviewId}", interview.Id);
                    return;
                }

                // Get the user who created the interview
                var createdByUser = await _roleManagementService.GetUserByIdAsync(createdByUserId);
                var createdByName = createdByUser != null
                    ? $"{createdByUser.FirstName} {createdByUser.LastName}".Trim()
                    : "RecruiterBot User";

                if (string.IsNullOrWhiteSpace(createdByName))
                {
                    createdByName = createdByUser?.Email ?? "RecruiterBot User";
                }

                var consultantName = $"{interview.Consultant.FirstName} {interview.Consultant.LastName}".Trim();
                if (string.IsNullOrWhiteSpace(consultantName))
                {
                    consultantName = interview.Consultant.Email;
                }

                var emailSent = await _emailService.SendInterviewAssignmentAsync(
                    interview.Consultant.Email,
                    consultantName,
                    interview,
                    createdByName);

                if (emailSent)
                {
                    _logger.LogInformation("Interview assignment notification sent to consultant {ConsultantEmail} for interview {InterviewId}",
                        interview.Consultant.Email, interview.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to send interview assignment notification to consultant {ConsultantEmail} for interview {InterviewId}",
                        interview.Consultant.Email, interview.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending interview assignment notification for interview {InterviewId}", interview.Id);
                // Don't throw - notification failure shouldn't prevent interview creation
            }
        }
    }
}
