@page
@model RecruiterBot.Web.Pages.Consultant.MyInterviewsModel
@{
    ViewData["Title"] = "My Assigned Interviews";
}

<div class="container-fluid">
    <!-- Upcoming Interviews Section -->
    @if (Model.UpcomingInterviews?.Any() == true)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-clock me-2"></i>
                            Upcoming Interviews (Next 7 Days)
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            @foreach (var interview in Model.UpcomingInterviews.Take(3))
                            {
                                <div class="col-md-4">
                                    <div class="card border-warning h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                @if (interview.Candidate != null)
                                                {
                                                    <span>@interview.Candidate.FullName</span>
                                                }
                                                else
                                                {
                                                    <span>Interview</span>
                                                }
                                            </h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    <span class="interview-datetime" data-utc="@interview.InterviewDateTimeUtc.ToString("yyyy-MM-ddTHH:mm:ssZ")">
                                                        @interview.InterviewDateTimeUtc.ToString("MMM dd, yyyy 'at' HH:mm UTC")
                                                    </span>
                                                </small>
                                            </p>
                                            <p class="card-text">
                                                <span class="@interview.StatusBadgeClass">@interview.StatusDisplayName</span>
                                                <span class="badge bg-info ms-2">@Model.GetTimeUntilInterview(interview.InterviewDateTimeUtc)</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                        @if (Model.UpcomingInterviews.Count() > 3)
                        {
                            <div class="text-center mt-3">
                                <small class="text-muted">And @(Model.UpcomingInterviews.Count() - 3) more upcoming interview(s)</small>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- All Interviews Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-calendar-event me-2"></i>
                            My Assigned Interviews
                            @if (Model.TotalCount > 0)
                            {
                                <span class="badge bg-primary ms-2">@Model.TotalCount</span>
                            }
                        </h3>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="get" class="d-flex">
                                <input type="hidden" name="page" value="1" />
                                <input type="hidden" name="statusFilter" value="@Model.StatusFilter" />
                                <div class="input-group">
                                    <input type="text" 
                                           name="searchTerm" 
                                           value="@Model.SearchTerm" 
                                           class="form-control" 
                                           placeholder="Search interviews..." />
                                    <button type="submit" class="btn btn-outline-secondary">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get">
                                <input type="hidden" name="page" value="1" />
                                <input type="hidden" name="searchTerm" value="@Model.SearchTerm" />
                                <select name="statusFilter" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Statuses</option>
                                    <option value="Scheduled" selected="@(Model.StatusFilter == "Scheduled")">Scheduled</option>
                                    <option value="InProgress" selected="@(Model.StatusFilter == "InProgress")">In Progress</option>
                                    <option value="Completed" selected="@(Model.StatusFilter == "Completed")">Completed</option>
                                    <option value="Cancelled" selected="@(Model.StatusFilter == "Cancelled")">Cancelled</option>
                                    <option value="Rescheduled" selected="@(Model.StatusFilter == "Rescheduled")">Rescheduled</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            @if (!string.IsNullOrWhiteSpace(Model.SearchTerm) || !string.IsNullOrWhiteSpace(Model.StatusFilter))
                            {
                                <a href="/Consultant/MyInterviews" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>
                                    Clear Filters
                                </a>
                            }
                        </div>
                    </div>

                    <!-- Interviews List -->
                    @if (Model.Interviews?.Any() == true)
                    {
                        <div class="row g-3">
                            @foreach (var interview in Model.Interviews)
                            {
                                <div class="col-12">
                                    <div class="card interview-card @(Model.IsUpcoming(interview.InterviewDateTimeUtc) ? "border-success" : Model.IsPast(interview.InterviewDateTimeUtc) ? "border-secondary" : "")">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="d-flex align-items-start justify-content-between mb-2">
                                                        <div>
                                                            <h5 class="card-title mb-1">
                                                                @if (interview.Candidate != null)
                                                                {
                                                                    <span>Interview with @interview.Candidate.FullName</span>
                                                                }
                                                                else
                                                                {
                                                                    <span>Interview (Candidate ID: @interview.CandidateId)</span>
                                                                }
                                                            </h5>
                                                            <div class="text-muted small mb-2">
                                                                <i class="bi bi-calendar me-1"></i>
                                                                <span class="interview-datetime" data-utc="@interview.InterviewDateTimeUtc.ToString("yyyy-MM-ddTHH:mm:ssZ")">
                                                                    @interview.InterviewDateTimeUtc.ToString("MMM dd, yyyy 'at' HH:mm UTC")
                                                                </span>
                                                                @if (Model.IsUpcoming(interview.InterviewDateTimeUtc))
                                                                {
                                                                    <span class="badge bg-success ms-2">@Model.GetTimeUntilInterview(interview.InterviewDateTimeUtc)</span>
                                                                }
                                                                else if (Model.IsPast(interview.InterviewDateTimeUtc) && interview.Status == RecruiterBot.Core.Models.InterviewStatus.Scheduled)
                                                                {
                                                                    <span class="badge bg-warning ms-2">Overdue</span>
                                                                }
                                                            </div>
                                                        </div>
                                                        <span class="@interview.StatusBadgeClass">@interview.StatusDisplayName</span>
                                                    </div>
                                                    
                                                    <div class="mb-2">
                                                        <strong>Job Description:</strong>
                                                        <p class="text-muted mb-1">
                                                            @(interview.JobDescription.Length > 200 ? interview.JobDescription.Substring(0, 200) + "..." : interview.JobDescription)
                                                        </p>
                                                    </div>

                                                    <div class="row text-muted small">
                                                        <div class="col-md-6">
                                                            <i class="bi bi-envelope me-1"></i>
                                                            <strong>Candidate Email:</strong>
                                                            @if (interview.Candidate != null)
                                                            {
                                                                <span>@interview.Candidate.Email</span>
                                                            }
                                                            else
                                                            {
                                                                <span>Not available</span>
                                                            }
                                                        </div>
                                                        <div class="col-md-6">
                                                            <i class="bi bi-cpu me-1"></i>
                                                            <strong>LLM Model:</strong>
                                                            @if (interview.LLMModel != null)
                                                            {
                                                                <span>@interview.LLMModel.DisplayName</span>
                                                            }
                                                            else
                                                            {
                                                                <span>ID: @interview.LLMModelId</span>
                                                            }
                                                        </div>
                                                    </div>

                                                    @if (!string.IsNullOrWhiteSpace(interview.Notes))
                                                    {
                                                        <div class="mt-2">
                                                            <strong>Notes:</strong>
                                                            <p class="text-muted mb-0">@interview.Notes</p>
                                                        </div>
                                                    }
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="d-flex flex-column gap-2">
                                                        @if (interview.Status != RecruiterBot.Core.Models.InterviewStatus.Completed && interview.Status != RecruiterBot.Core.Models.InterviewStatus.Cancelled)
                                                        {
                                                            <div class="dropdown">
                                                                <button class="btn btn-sm btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                    <i class="bi bi-gear me-1"></i>
                                                                    Update Status
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    @{
                                                                        var statusOptions = Model.GetStatusUpdateOptions(interview.Status).Split(',', StringSplitOptions.RemoveEmptyEntries);
                                                                    }
                                                                    @foreach (var status in statusOptions)
                                                                    {
                                                                        <li>
                                                                            <a class="dropdown-item status-update-link" 
                                                                               href="#" 
                                                                               data-interview-id="@interview.Id" 
                                                                               data-status="@status">
                                                                                @status.Replace("InProgress", "In Progress")
                                                                            </a>
                                                                        </li>
                                                                    }
                                                                </ul>
                                                            </div>
                                                        }
                                                        
                                                        <a href="/Interviews/Details?id=@interview.Id" class="btn btn-sm btn-outline-info">
                                                            <i class="bi bi-eye me-1"></i>
                                                            View Details
                                                        </a>

                                                        @if (interview.Candidate != null && !string.IsNullOrWhiteSpace(interview.Candidate.Email))
                                                        {
                                                            <a href="mailto:@interview.Candidate.Email" class="btn btn-sm btn-outline-secondary">
                                                                <i class="bi bi-envelope me-1"></i>
                                                                Contact Candidate
                                                            </a>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Interviews pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (Model.CurrentPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="?page=@(Model.CurrentPage - 1)@(!string.IsNullOrWhiteSpace(Model.SearchTerm) ? "&searchTerm=" + Model.SearchTerm : "")@(!string.IsNullOrWhiteSpace(Model.StatusFilter) ? "&statusFilter=" + Model.StatusFilter : "")">Previous</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" href="?page=@i@(!string.IsNullOrWhiteSpace(Model.SearchTerm) ? "&searchTerm=" + Model.SearchTerm : "")@(!string.IsNullOrWhiteSpace(Model.StatusFilter) ? "&statusFilter=" + Model.StatusFilter : "")">@i</a>
                                        </li>
                                    }

                                    @if (Model.CurrentPage < Model.TotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="?page=@(Model.CurrentPage + 1)@(!string.IsNullOrWhiteSpace(Model.SearchTerm) ? "&searchTerm=" + Model.SearchTerm : "")@(!string.IsNullOrWhiteSpace(Model.StatusFilter) ? "&statusFilter=" + Model.StatusFilter : "")">Next</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-x display-1 text-muted"></i>
                            <h4 class="mt-3">No Interviews Found</h4>
                            <p class="text-muted">
                                @if (!string.IsNullOrWhiteSpace(Model.SearchTerm) || !string.IsNullOrWhiteSpace(Model.StatusFilter))
                                {
                                    <span>No interviews match your search criteria.</span>
                                }
                                else
                                {
                                    <span>You haven't been assigned any interviews yet.</span>
                                }
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Convert UTC times to local timezone
            document.querySelectorAll('.interview-datetime').forEach(function(element) {
                const utcTime = element.getAttribute('data-utc');
                if (utcTime) {
                    const localTime = new Date(utcTime);
                    element.textContent = localTime.toLocaleString();
                }
            });

            // Handle status updates
            document.querySelectorAll('.status-update-link').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const interviewId = this.getAttribute('data-interview-id');
                    const status = this.getAttribute('data-status');
                    updateInterviewStatus(interviewId, status);
                });
            });
        });

        function updateInterviewStatus(interviewId, status) {
            if (confirm('Are you sure you want to update the interview status to ' + status.replace('InProgress', 'In Progress') + '?')) {
                fetch('/Consultant/MyInterviews?handler=UpdateStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: new URLSearchParams({
                        interviewId: interviewId,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status.');
                });
            }
        }
    </script>
}
